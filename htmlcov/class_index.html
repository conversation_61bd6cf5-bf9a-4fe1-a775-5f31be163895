<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">47%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.7">coverage.py v7.10.7</a>,
            created at 2025-10-04 13:22 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb___init___py.html">src/suno_ai_client/__init__.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>170</td>
                <td>170</td>
                <td>0</td>
                <td class="right" data-ratio="0 170">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t26">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t26"><data value='SunoClient'>SunoClient</data></a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t11">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t11"><data value='SunoConfig'>SunoConfig</data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t6">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t6"><data value='SunoAPIError'>SunoAPIError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t21">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t21"><data value='SunoAuthError'>SunoAuthError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t28">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t28"><data value='SunoValidationError'>SunoValidationError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t41">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t41"><data value='SunoNetworkError'>SunoNetworkError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t48">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t48"><data value='SunoRateLimitError'>SunoRateLimitError</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t62">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t62"><data value='SunoFileError'>SunoFileError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t9">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t9"><data value='ExtendRequest'>ExtendRequest</data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t77">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t77"><data value='ExtendResponse'>ExtendResponse</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t101">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t101"><data value='UploadResponse'>UploadResponse</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>96</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="88 96">92%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>547</td>
                <td>289</td>
                <td>0</td>
                <td class="right" data-ratio="258 547">47%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.7">coverage.py v7.10.7</a>,
            created at 2025-10-04 13:22 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
