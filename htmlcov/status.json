{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.7", "globals": "21d9d6fa9ddbbd45b6d07ee9b32677c2", "files": {"z_0fce8e6cb36d7acb___init___py": {"hash": "7bb9b0a97ea070c1dd4705b2a2f4a118", "index": {"url": "z_0fce8e6cb36d7acb___init___py.html", "file": "src/suno_ai_client/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0fce8e6cb36d7acb_cli_py": {"hash": "dbf1592fa3228dd6a8d86bba28786129", "index": {"url": "z_0fce8e6cb36d7acb_cli_py.html", "file": "src/suno_ai_client/cli.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 170, "n_excluded": 0, "n_missing": 170, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0fce8e6cb36d7acb_client_py": {"hash": "083ae51f7e07895e77c399935cc3416d", "index": {"url": "z_0fce8e6cb36d7acb_client_py.html", "file": "src/suno_ai_client/client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 107, "n_excluded": 0, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0fce8e6cb36d7acb_config_py": {"hash": "8794bf0d4e4ae4b8ec3aca98d4fdf2a6", "index": {"url": "z_0fce8e6cb36d7acb_config_py.html", "file": "src/suno_ai_client/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0fce8e6cb36d7acb_exceptions_py": {"hash": "82d2c23ea3534c132f92414a11940088", "index": {"url": "z_0fce8e6cb36d7acb_exceptions_py.html", "file": "src/suno_ai_client/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0fce8e6cb36d7acb_models_py": {"hash": "cd992c9f54a2ccae718533c1b1c97bde", "index": {"url": "z_0fce8e6cb36d7acb_models_py.html", "file": "src/suno_ai_client/models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 69, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_0fce8e6cb36d7acb_utils_py": {"hash": "70981437b433b37175bcde01277ea1e0", "index": {"url": "z_0fce8e6cb36d7acb_utils_py.html", "file": "src/suno_ai_client/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 96, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}