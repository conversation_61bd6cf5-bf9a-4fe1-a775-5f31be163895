<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">47%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.7">coverage.py v7.10.7</a>,
            created at 2025-10-04 13:22 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb___init___py.html">src/suno_ai_client/__init__.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t24">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t24"><data value='handle_error'>handle_error</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t26">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t26"><data value='wrapper'>handle_error.wrapper</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t50">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t50"><data value='cli'>cli</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t78">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t78"><data value='upload'>upload</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t126">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t126"><data value='extend'>extend</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t181">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t181"><data value='display_extend_result'>display_extend_result</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t215">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t215"><data value='status'>status</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t236">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t236"><data value='display_task_status'>display_task_status</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t261">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t261"><data value='config'>config</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t278">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html#t278"><data value='main'>main</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html">src/suno_ai_client/cli.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_cli_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t29">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t29"><data value='init__'>SunoClient.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t47">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t47"><data value='create_session'>SunoClient._create_session</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t68">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t68"><data value='setup_logging'>SunoClient._setup_logging</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t83">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t83"><data value='handle_response'>SunoClient._handle_response</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t108">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t108"><data value='upload_file'>SunoClient.upload_file</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t161">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t161"><data value='extend_audio'>SunoClient.extend_audio</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t193">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t193"><data value='extend_from_file'>SunoClient.extend_from_file</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t226">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t226"><data value='get_task_status'>SunoClient.get_task_status</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t246">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html#t246"><data value='wait_for_completion'>SunoClient.wait_for_completion</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html">src/suno_ai_client/client.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t27">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t27"><data value='validate_api_token'>SunoConfig.validate_api_token</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t34">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t34"><data value='validate_api_base_url'>SunoConfig.validate_api_base_url</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t41">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t41"><data value='validate_log_level'>SunoConfig.validate_log_level</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t49">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t49"><data value='validate_timeout'>SunoConfig.validate_timeout</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t56">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t56"><data value='validate_max_retries'>SunoConfig.validate_max_retries</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t62">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t62"><data value='from_env'>SunoConfig.from_env</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t80">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t80"><data value='from_dict'>SunoConfig.from_dict</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t84">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t84"><data value='get_headers'>SunoConfig.get_headers</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t92">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html#t92"><data value='get_upload_extend_url'>SunoConfig.get_upload_extend_url</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html">src/suno_ai_client/config.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t9">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t9"><data value='init__'>SunoAPIError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t15">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t15"><data value='str__'>SunoAPIError.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t24">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t24"><data value='init__'>SunoAuthError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t31">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t31"><data value='init__'>SunoValidationError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t35">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t35"><data value='str__'>SunoValidationError.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t44">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t44"><data value='init__'>SunoNetworkError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t51">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t51"><data value='init__'>SunoRateLimitError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t55">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t55"><data value='str__'>SunoRateLimitError.__str__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t65">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t65"><data value='init__'>SunoFileError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t69">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html#t69"><data value='str__'>SunoFileError.__str__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html">src/suno_ai_client/exceptions.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t31">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t31"><data value='validate_vocal_gender'>ExtendRequest.validate_vocal_gender</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t38">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t38"><data value='validate_model'>ExtendRequest.validate_model</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t44">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t44"><data value='to_api_dict'>ExtendRequest.to_api_dict</data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t88">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t88"><data value='from_api_response'>ExtendResponse.from_api_response</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t110">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html#t110"><data value='from_api_response'>UploadResponse.from_api_response</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html">src/suno_ai_client/models.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t10">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t10"><data value='is_audio_file'>is_audio_file</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t37">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t37"><data value='get_supported_audio_formats'>get_supported_audio_formats</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t50">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t50"><data value='format_file_size'>format_file_size</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t74">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t74"><data value='validate_audio_file'>validate_audio_file</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t107">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t107"><data value='sanitize_filename'>sanitize_filename</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t132">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html#t132"><data value='parse_time_string'>parse_time_string</data></a></td>
                <td>49</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="41 49">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html">src/suno_ai_client/utils.py</a></td>
                <td class="name left"><a href="z_0fce8e6cb36d7acb_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>547</td>
                <td>289</td>
                <td>0</td>
                <td class="right" data-ratio="258 547">47%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.7">coverage.py v7.10.7</a>,
            created at 2025-10-04 13:22 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
