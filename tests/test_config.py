"""
Tests for configuration management.
"""

import os
import tempfile
from pathlib import Path
import pytest

from suno_ai_client.config import SunoConfig


class TestSunoConfig:
    """Test SunoConfig class."""
    
    def test_default_config(self):
        """Test creating config with minimal required parameters."""
        config = SunoConfig(api_token="test_token")
        
        assert config.api_token == "test_token"
        assert config.api_base_url == "https://api.sunoapi.org/api/v1"
        assert config.default_model == "V5"
        assert config.timeout == 30
        assert config.max_retries == 3
        assert config.log_level == "INFO"
        assert config.callback_url is None
    
    def test_custom_config(self):
        """Test creating config with custom parameters."""
        config = SunoConfig(
            api_token="custom_token",
            api_base_url="https://custom.api.com/v2",
            default_model="V4",
            timeout=60,
            max_retries=5,
            log_level="DEBUG",
            callback_url="https://callback.com/webhook"
        )
        
        assert config.api_token == "custom_token"
        assert config.api_base_url == "https://custom.api.com/v2"
        assert config.default_model == "V4"
        assert config.timeout == 60
        assert config.max_retries == 5
        assert config.log_level == "DEBUG"
        assert config.callback_url == "https://callback.com/webhook"
    
    def test_api_token_validation(self):
        """Test API token validation."""
        # Empty token should raise error
        with pytest.raises(ValueError, match="API token cannot be empty"):
            SunoConfig(api_token="")
        
        # Whitespace-only token should raise error
        with pytest.raises(ValueError, match="API token cannot be empty"):
            SunoConfig(api_token="   ")
        
        # Valid token should work
        config = SunoConfig(api_token="  valid_token  ")
        assert config.api_token == "valid_token"  # Should be stripped
    
    def test_api_base_url_validation(self):
        """Test API base URL validation."""
        # Invalid URL should raise error
        with pytest.raises(ValueError, match="API base URL must start with http"):
            SunoConfig(api_token="token", api_base_url="invalid-url")
        
        # Valid URLs should work
        config1 = SunoConfig(api_token="token", api_base_url="http://api.com")
        assert config1.api_base_url == "http://api.com"
        
        config2 = SunoConfig(api_token="token", api_base_url="https://api.com/")
        assert config2.api_base_url == "https://api.com"  # Trailing slash removed
    
    def test_log_level_validation(self):
        """Test log level validation."""
        # Invalid log level should raise error
        with pytest.raises(ValueError, match="log_level must be one of"):
            SunoConfig(api_token="token", log_level="INVALID")
        
        # Valid log levels should work
        for level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            config = SunoConfig(api_token="token", log_level=level.lower())
            assert config.log_level == level
    
    def test_timeout_validation(self):
        """Test timeout validation."""
        # Zero or negative timeout should raise error
        with pytest.raises(ValueError, match="timeout must be positive"):
            SunoConfig(api_token="token", timeout=0)
        
        with pytest.raises(ValueError, match="timeout must be positive"):
            SunoConfig(api_token="token", timeout=-1)
        
        # Positive timeout should work
        config = SunoConfig(api_token="token", timeout=60)
        assert config.timeout == 60
    
    def test_max_retries_validation(self):
        """Test max retries validation."""
        # Negative retries should raise error
        with pytest.raises(ValueError, match="max_retries cannot be negative"):
            SunoConfig(api_token="token", max_retries=-1)
        
        # Zero and positive retries should work
        config1 = SunoConfig(api_token="token", max_retries=0)
        assert config1.max_retries == 0
        
        config2 = SunoConfig(api_token="token", max_retries=5)
        assert config2.max_retries == 5
    
    def test_from_env(self, monkeypatch):
        """Test loading configuration from environment variables."""
        # Set environment variables
        monkeypatch.setenv("SUNO_API_TOKEN", "env_token")
        monkeypatch.setenv("SUNO_API_BASE_URL", "https://env.api.com")
        monkeypatch.setenv("SUNO_DEFAULT_MODEL", "V4")
        monkeypatch.setenv("SUNO_CALLBACK_URL", "https://env.callback.com")
        monkeypatch.setenv("SUNO_TIMEOUT", "45")
        monkeypatch.setenv("SUNO_MAX_RETRIES", "7")
        monkeypatch.setenv("LOG_LEVEL", "WARNING")
        
        config = SunoConfig.from_env()
        
        assert config.api_token == "env_token"
        assert config.api_base_url == "https://env.api.com"
        assert config.default_model == "V4"
        assert config.callback_url == "https://env.callback.com"
        assert config.timeout == 45
        assert config.max_retries == 7
        assert config.log_level == "WARNING"
    
    def test_from_env_with_file(self, monkeypatch):
        """Test loading configuration from a specific .env file."""
        # Create temporary .env file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write("SUNO_API_TOKEN=file_token\n")
            f.write("SUNO_API_BASE_URL=https://file.api.com\n")
            f.write("LOG_LEVEL=ERROR\n")
            env_file = f.name
        
        try:
            config = SunoConfig.from_env(env_file)
            
            assert config.api_token == "file_token"
            assert config.api_base_url == "https://file.api.com"
            assert config.log_level == "ERROR"
            # Other values should be defaults
            assert config.default_model == "V5"
            assert config.timeout == 30
        finally:
            os.unlink(env_file)
    
    def test_from_dict(self):
        """Test creating configuration from dictionary."""
        config_dict = {
            "api_token": "dict_token",
            "api_base_url": "https://dict.api.com",
            "default_model": "V5",
            "timeout": 90,
            "max_retries": 2,
            "log_level": "DEBUG",
            "callback_url": "https://dict.callback.com"
        }
        
        config = SunoConfig.from_dict(config_dict)
        
        assert config.api_token == "dict_token"
        assert config.api_base_url == "https://dict.api.com"
        assert config.default_model == "V5"
        assert config.timeout == 90
        assert config.max_retries == 2
        assert config.log_level == "DEBUG"
        assert config.callback_url == "https://dict.callback.com"
    
    def test_get_headers(self):
        """Test getting HTTP headers."""
        config = SunoConfig(api_token="test_token")
        headers = config.get_headers()
        
        assert headers["Authorization"] == "Bearer test_token"
        assert headers["Content-Type"] == "application/json"
        assert "User-Agent" in headers
        assert "suno-ai-client" in headers["User-Agent"]
    
    def test_get_upload_extend_url(self):
        """Test getting the upload-extend endpoint URL."""
        config = SunoConfig(
            api_token="token",
            api_base_url="https://api.example.com/v1"
        )
        
        url = config.get_upload_extend_url()
        assert url == "https://api.example.com/v1/generate/upload-extend"
