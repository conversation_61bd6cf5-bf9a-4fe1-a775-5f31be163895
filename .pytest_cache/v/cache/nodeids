["tests/test_config.py::TestSunoConfig::test_api_base_url_validation", "tests/test_config.py::TestSunoConfig::test_api_token_validation", "tests/test_config.py::TestSunoConfig::test_custom_config", "tests/test_config.py::TestSunoConfig::test_default_config", "tests/test_config.py::TestSunoConfig::test_from_dict", "tests/test_config.py::TestSunoConfig::test_from_env", "tests/test_config.py::TestSunoConfig::test_from_env_with_file", "tests/test_config.py::TestSunoConfig::test_get_headers", "tests/test_config.py::TestSunoConfig::test_get_upload_extend_url", "tests/test_config.py::TestSunoConfig::test_log_level_validation", "tests/test_config.py::TestSunoConfig::test_max_retries_validation", "tests/test_config.py::TestSunoConfig::test_timeout_validation", "tests/test_models.py::TestExtendRequest::test_continue_at_validation", "tests/test_models.py::TestExtendRequest::test_full_request", "tests/test_models.py::TestExtendRequest::test_minimal_request", "tests/test_models.py::TestExtendRequest::test_model_validation", "tests/test_models.py::TestExtendRequest::test_to_api_dict", "tests/test_models.py::TestExtendRequest::test_to_api_dict_minimal", "tests/test_models.py::TestExtendRequest::test_vocal_gender_validation", "tests/test_models.py::TestExtendRequest::test_weight_validation", "tests/test_models.py::TestExtendResponse::test_basic_response", "tests/test_models.py::TestExtendResponse::test_from_api_response", "tests/test_models.py::TestExtendResponse::test_from_api_response_snake_case", "tests/test_models.py::TestUploadResponse::test_basic_response", "tests/test_models.py::TestUploadResponse::test_from_api_response", "tests/test_models.py::TestUploadResponse::test_from_api_response_snake_case", "tests/test_utils.py::TestAudioFileDetection::test_get_supported_audio_formats", "tests/test_utils.py::TestAudioFileDetection::test_is_audio_file_directory", "tests/test_utils.py::TestAudioFileDetection::test_is_audio_file_non_audio", "tests/test_utils.py::TestAudioFileDetection::test_is_audio_file_nonexistent", "tests/test_utils.py::TestAudioFileDetection::test_is_audio_file_with_extensions", "tests/test_utils.py::TestAudioFileValidation::test_validate_audio_file_directory", "tests/test_utils.py::TestAudioFileValidation::test_validate_audio_file_empty", "tests/test_utils.py::TestAudioFileValidation::test_validate_audio_file_non_audio", "tests/test_utils.py::TestAudioFileValidation::test_validate_audio_file_nonexistent", "tests/test_utils.py::TestAudioFileValidation::test_validate_audio_file_too_large", "tests/test_utils.py::TestAudioFileValidation::test_validate_audio_file_valid", "tests/test_utils.py::TestFileSizeFormatting::test_format_file_size_bytes", "tests/test_utils.py::TestFileSizeFormatting::test_format_file_size_gigabytes", "tests/test_utils.py::TestFileSizeFormatting::test_format_file_size_kilobytes", "tests/test_utils.py::TestFileSizeFormatting::test_format_file_size_megabytes", "tests/test_utils.py::TestFilenameUtils::test_sanitize_filename_basic", "tests/test_utils.py::TestFilenameUtils::test_sanitize_filename_empty", "tests/test_utils.py::TestFilenameUtils::test_sanitize_filename_invalid_chars", "tests/test_utils.py::TestFilenameUtils::test_sanitize_filename_whitespace", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_edge_cases", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_hh_mm_ss", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_invalid", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_mixed_case", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_mm_ss", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_seconds", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_with_spaces", "tests/test_utils.py::TestTimeStringParsing::test_parse_time_string_with_units"]