Metadata-Version: 2.4
Name: suno-ai-client
Version: 0.1.0
Summary: Easy-to-use interface for uploading and extending audio tracks using Suno AI's API
Author-email: Suno AI Client <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/example/suno-ai-client
Project-URL: Documentation, https://github.com/example/suno-ai-client#readme
Project-URL: Repository, https://github.com/example/suno-ai-client.git
Project-URL: Bug Tracker, https://github.com/example/suno-ai-client/issues
Keywords: suno,ai,audio,music,api,extension
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: requests>=2.28.0
Requires-Dist: click>=8.0.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: rich>=13.0.0
Requires-Dist: typing-extensions>=4.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: pre-commit>=3.0.0; extra == "dev"
Dynamic: license-file

# Suno AI Client

A comprehensive Python client for interacting with Suno AI's API to upload and extend audio tracks using artificial intelligence.

## Features

- 🎵 **Easy Audio Upload**: Upload audio files to Suno AI with automatic validation
- 🤖 **AI-Powered Extension**: Extend your audio tracks using text prompts
- 🛠️ **Flexible Configuration**: Environment variables, config files, or programmatic setup
- 🖥️ **CLI Interface**: Command-line tool for quick operations
- 📦 **Programmatic API**: Full Python API for integration into your projects
- 🔄 **Async Support**: Handle long-running operations with task tracking
- 🛡️ **Error Handling**: Comprehensive error handling and validation
- 📝 **Type Hints**: Full type annotation support

## Installation

```bash
pip install suno-ai-client
```

Or install from source:

```bash
git clone https://github.com/example/suno-ai-client.git
cd suno-ai-client
pip install -e .
```

## Quick Start

### 1. Configuration

Create a `.env` file in your project root:

```env
SUNO_API_TOKEN=your_bearer_token_here
```

Or set environment variables:

```bash
export SUNO_API_TOKEN="your_bearer_token_here"
```

### 2. Command Line Usage

```bash
# Upload and extend an audio file
suno-ai extend my_audio.mp3 "Add a beautiful piano melody to extend this track"

# Upload and extend with specific parameters
suno-ai extend my_audio.mp3 "Make it more jazzy" \
  --style "Jazz" \
  --title "Extended Jazz Track" \
  --continue-at 60 \
  --wait

# Check task status
suno-ai status task_id_here

# Show configuration
suno-ai config
```

### 3. Python API Usage

```python
from suno_ai_client import SunoClient, ExtendRequest

# Initialize client (loads config from environment)
client = SunoClient()

# Or initialize with explicit token
client = SunoClient(api_token="your_token_here")

# Upload and extend in one operation
response = client.extend_from_file(
    file_path="my_audio.mp3",
    prompt="Add orchestral elements to make it more epic",
    style="Orchestral",
    title="Epic Extended Version",
    continue_at=45
)

print(f"Task ID: {response.task_id}")

# Wait for completion
if response.task_id:
    final_result = client.wait_for_completion(response.task_id)
    print(f"Audio URL: {final_result.get('audio_url')}")
```

## API Reference

### SunoClient

The main client class for interacting with Suno AI's API.

#### Methods

- `upload_file(file_path)` - Upload an audio file
- `extend_audio(request)` - Extend audio with an ExtendRequest
- `extend_from_file(file_path, prompt, **kwargs)` - Upload and extend in one call
- `get_task_status(task_id)` - Get status of an async task
- `wait_for_completion(task_id, max_wait_time, poll_interval)` - Wait for task completion

### ExtendRequest

Request model for audio extension operations.

#### Parameters

- `upload_url` (str, required) - URL of uploaded audio file
- `prompt` (str, required) - Text prompt for extension
- `style` (str, optional) - Musical style (e.g., "Classical", "Jazz")
- `title` (str, optional) - Title for the extended track
- `continue_at` (int, optional) - Time in seconds to continue from
- `model` (str, default="V5") - Suno AI model version
- `instrumental` (bool, default=True) - Generate instrumental music
- `vocal_gender` (str, optional) - Vocal gender preference ("m" or "f")
- `style_weight` (float, optional) - Style influence weight (0.0-1.0)
- `weirdness_constraint` (float, optional) - Creativity constraint (0.0-1.0)
- `audio_weight` (float, optional) - Original audio influence (0.0-1.0)
- `negative_tags` (str, optional) - Tags to avoid in generation
- `callback_url` (str, optional) - URL for async operation callbacks

## Configuration

### Environment Variables

- `SUNO_API_TOKEN` - Your Suno AI API bearer token (required)
- `SUNO_API_BASE_URL` - API base URL (default: https://api.sunoapi.org/api/v1)
- `SUNO_DEFAULT_MODEL` - Default model version (default: V5)
- `SUNO_CALLBACK_URL` - Default callback URL for async operations
- `SUNO_TIMEOUT` - Request timeout in seconds (default: 30)
- `SUNO_MAX_RETRIES` - Maximum number of retries (default: 3)
- `LOG_LEVEL` - Logging level (default: INFO)

### Configuration File

You can also use a custom `.env` file:

```python
from suno_ai_client import SunoConfig, SunoClient

config = SunoConfig.from_env("path/to/custom.env")
client = SunoClient(config)
```

## Examples

See the `examples/` directory for more detailed examples:

- `basic_usage.py` - Basic upload and extend operations
- `advanced_usage.py` - Advanced features and error handling
- `async_operations.py` - Working with async tasks
- `batch_processing.py` - Processing multiple files

## Error Handling

The client provides specific exception types for different error scenarios:

```python
from suno_ai_client.exceptions import (
    SunoAPIError,
    SunoAuthError,
    SunoFileError,
    SunoNetworkError,
    SunoRateLimitError
)

try:
    response = client.extend_from_file("audio.mp3", "Make it jazzy")
except SunoAuthError:
    print("Invalid API token")
except SunoFileError as e:
    print(f"File error: {e}")
except SunoRateLimitError as e:
    print(f"Rate limited. Retry after {e.retry_after} seconds")
except SunoAPIError as e:
    print(f"API error: {e}")
```

## Development

### Setup Development Environment

```bash
git clone https://github.com/example/suno-ai-client.git
cd suno-ai-client
pip install -e ".[dev]"
```

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black src/ tests/
isort src/ tests/
```

### Type Checking

```bash
mypy src/
```

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please read CONTRIBUTING.md for guidelines.

## Support

- GitHub Issues: https://github.com/example/suno-ai-client/issues
- Documentation: https://github.com/example/suno-ai-client#readme
