"""
Utility functions for Suno AI Client.
"""

import mimetypes
from pathlib import Path
from typing import List, Optional


def is_audio_file(file_path: Path) -> bool:
    """
    Check if a file is an audio file based on its extension and MIME type.
    
    Args:
        file_path: Path to the file to check.
        
    Returns:
        True if the file appears to be an audio file.
    """
    if not file_path.exists() or not file_path.is_file():
        return False
    
    # Check MIME type
    mime_type, _ = mimetypes.guess_type(str(file_path))
    if mime_type and mime_type.startswith('audio/'):
        return True
    
    # Check common audio extensions
    audio_extensions = {
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', 
        '.wma', '.aiff', '.au', '.ra', '.3gp', '.amr'
    }
    
    return file_path.suffix.lower() in audio_extensions


def get_supported_audio_formats() -> List[str]:
    """
    Get a list of supported audio formats.
    
    Returns:
        List of supported file extensions.
    """
    return [
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a',
        '.wma', '.aiff', '.au', '.ra', '.3gp', '.amr'
    ]


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes.
        
    Returns:
        Formatted size string (e.g., "1.5 MB").
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"


def validate_audio_file(file_path: Path, max_size_mb: int = 50) -> Optional[str]:
    """
    Validate an audio file for upload.
    
    Args:
        file_path: Path to the audio file.
        max_size_mb: Maximum file size in MB.
        
    Returns:
        Error message if validation fails, None if valid.
    """
    if not file_path.exists():
        return f"File does not exist: {file_path}"
    
    if not file_path.is_file():
        return f"Path is not a file: {file_path}"
    
    if not is_audio_file(file_path):
        supported = ", ".join(get_supported_audio_formats())
        return f"File does not appear to be an audio file. Supported formats: {supported}"
    
    file_size = file_path.stat().st_size
    max_size_bytes = max_size_mb * 1024 * 1024
    
    if file_size > max_size_bytes:
        return f"File too large: {format_file_size(file_size)} (max: {max_size_mb} MB)"
    
    if file_size == 0:
        return "File is empty"
    
    return None


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename for safe usage.
    
    Args:
        filename: Original filename.
        
    Returns:
        Sanitized filename.
    """
    # Remove or replace problematic characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove leading/trailing whitespace and dots
    filename = filename.strip(' .')
    
    # Ensure filename is not empty
    if not filename:
        filename = "untitled"
    
    return filename


def parse_time_string(time_str: str) -> Optional[int]:
    """
    Parse a time string into seconds.
    
    Args:
        time_str: Time string in formats like "1:30", "90", "1m30s".
        
    Returns:
        Time in seconds, or None if parsing fails.
    """
    if not time_str:
        return None
    
    time_str = time_str.strip().lower()
    
    # Try parsing as plain seconds
    try:
        return int(float(time_str))
    except ValueError:
        pass
    
    # Try parsing MM:SS format
    if ':' in time_str:
        try:
            parts = time_str.split(':')
            if len(parts) == 2:
                minutes, seconds = parts
                return int(minutes) * 60 + int(float(seconds))
            elif len(parts) == 3:
                hours, minutes, seconds = parts
                return int(hours) * 3600 + int(minutes) * 60 + int(float(seconds))
        except ValueError:
            pass
    
    # Try parsing with units (e.g., "1m30s", "90s")
    total_seconds = 0
    current_number = ""
    found_valid_unit = False

    for char in time_str:
        if char.isdigit() or char == '.':
            current_number += char
        elif char in 'hms':
            if current_number:
                try:
                    value = float(current_number)
                    if char == 'h':
                        total_seconds += value * 3600
                    elif char == 'm':
                        total_seconds += value * 60
                    elif char == 's':
                        total_seconds += value
                    current_number = ""
                    found_valid_unit = True
                except ValueError:
                    return None
        elif not char.isspace():
            # Invalid character found
            return None

    # Handle trailing number without unit (assume seconds)
    if current_number:
        try:
            total_seconds += float(current_number)
            found_valid_unit = True
        except ValueError:
            return None

    # If no valid units or numbers were found, return None
    if not found_valid_unit:
        return None

    return int(total_seconds) if total_seconds >= 0 else None
