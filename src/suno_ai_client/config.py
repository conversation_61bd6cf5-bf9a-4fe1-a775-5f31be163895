"""
Configuration management for Suno AI Client.
"""

import os
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseModel, Field, field_validator


class SunoConfig(BaseModel):
    """Configuration settings for Suno AI Client."""
    
    api_token: str = Field(..., description="Suno AI API Bearer token")
    api_base_url: str = Field(
        default="https://api.sunoapi.org/api/v1",
        description="Base URL for Suno AI API"
    )
    default_model: str = Field(default="V5", description="Default model version")
    callback_url: Optional[str] = Field(None, description="Default callback URL")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum number of retries")
    log_level: str = Field(default="INFO", description="Logging level")
    
    @field_validator('api_token')
    @classmethod
    def validate_api_token(cls, v):
        if not v or not v.strip():
            raise ValueError("API token cannot be empty")
        return v.strip()

    @field_validator('api_base_url')
    @classmethod
    def validate_api_base_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("API base URL must start with http:// or https://")
        return v.rstrip('/')

    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"log_level must be one of {valid_levels}")
        return v.upper()

    @field_validator('timeout')
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0:
            raise ValueError("timeout must be positive")
        return v

    @field_validator('max_retries')
    @classmethod
    def validate_max_retries(cls, v):
        if v < 0:
            raise ValueError("max_retries cannot be negative")
        return v
    
    @classmethod
    def from_env(cls, env_file: Optional[str] = None) -> "SunoConfig":
        """Load configuration from environment variables."""
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()  # Load from .env file if it exists
        
        return cls(
            api_token=os.getenv("SUNO_API_TOKEN", ""),
            api_base_url=os.getenv("SUNO_API_BASE_URL", "https://api.sunoapi.org/api/v1"),
            default_model=os.getenv("SUNO_DEFAULT_MODEL", "V5"),
            callback_url=os.getenv("SUNO_CALLBACK_URL"),
            timeout=int(os.getenv("SUNO_TIMEOUT", "30")),
            max_retries=int(os.getenv("SUNO_MAX_RETRIES", "3")),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
        )
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> "SunoConfig":
        """Create configuration from dictionary."""
        return cls(**config_dict)
    
    def get_headers(self) -> dict:
        """Get HTTP headers for API requests."""
        return {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json",
            "User-Agent": "suno-ai-client/0.1.0",
        }
    
    def get_upload_extend_url(self) -> str:
        """Get the full URL for the upload-extend endpoint."""
        return f"{self.api_base_url}/generate/upload-extend"
