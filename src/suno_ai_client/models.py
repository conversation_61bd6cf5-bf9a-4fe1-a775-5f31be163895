"""
Data models for Suno AI API requests and responses.
"""

from typing import Optional, Union
from pydantic import BaseModel, Field, field_validator


class ExtendRequest(BaseModel):
    """Request model for extending audio tracks via Suno AI API."""
    
    upload_url: str = Field(..., description="URL of the uploaded audio file")
    prompt: str = Field(..., description="Text prompt for extending the music")
    
    # Optional parameters with defaults
    default_param_flag: bool = Field(True, description="Use default parameters")
    instrumental: bool = Field(True, description="Generate instrumental music")
    style: Optional[str] = Field(None, description="Musical style (e.g., 'Classical', 'Jazz')")
    title: Optional[str] = Field(None, description="Title for the extended track")
    continue_at: Optional[int] = Field(None, description="Time in seconds to continue from", ge=0)
    model: str = Field("V5", description="Suno AI model version")
    negative_tags: Optional[str] = Field(None, description="Tags to avoid in generation")
    vocal_gender: Optional[str] = Field(None, description="Vocal gender preference ('m' or 'f')")
    style_weight: Optional[float] = Field(None, description="Style influence weight", ge=0.0, le=1.0)
    weirdness_constraint: Optional[float] = Field(None, description="Creativity constraint", ge=0.0, le=1.0)
    audio_weight: Optional[float] = Field(None, description="Original audio influence", ge=0.0, le=1.0)
    callback_url: Optional[str] = Field(None, description="URL for async operation callbacks")
    
    @field_validator('vocal_gender')
    @classmethod
    def validate_vocal_gender(cls, v):
        if v is not None and v not in ['m', 'f']:
            raise ValueError("vocal_gender must be 'm' or 'f'")
        return v

    @field_validator('model')
    @classmethod
    def validate_model(cls, v):
        valid_models = ['V5', 'V3', 'V2']  # Add more as they become available
        if v not in valid_models:
            raise ValueError(f"model must be one of {valid_models}")
        return v
    
    def to_api_dict(self) -> dict:
        """Convert to dictionary format expected by Suno AI API."""
        data = {
            "uploadUrl": self.upload_url,
            "prompt": self.prompt,
            "defaultParamFlag": self.default_param_flag,
            "instrumental": self.instrumental,
            "model": self.model,
        }
        
        # Add optional fields if they are set
        if self.style is not None:
            data["style"] = self.style
        if self.title is not None:
            data["title"] = self.title
        if self.continue_at is not None:
            data["continueAt"] = self.continue_at
        if self.negative_tags is not None:
            data["negativeTags"] = self.negative_tags
        if self.vocal_gender is not None:
            data["vocalGender"] = self.vocal_gender
        if self.style_weight is not None:
            data["styleWeight"] = self.style_weight
        if self.weirdness_constraint is not None:
            data["weirdnessConstraint"] = self.weirdness_constraint
        if self.audio_weight is not None:
            data["audioWeight"] = self.audio_weight
        if self.callback_url is not None:
            data["callBackUrl"] = self.callback_url
            
        return data


class ExtendResponse(BaseModel):
    """Response model from Suno AI API."""
    
    success: bool = Field(..., description="Whether the request was successful")
    message: Optional[str] = Field(None, description="Response message")
    task_id: Optional[str] = Field(None, description="Task ID for tracking async operations")
    audio_url: Optional[str] = Field(None, description="URL of the generated audio")
    status: Optional[str] = Field(None, description="Current status of the generation")
    estimated_completion_time: Optional[int] = Field(None, description="ETA in seconds")
    
    @classmethod
    def from_api_response(cls, response_data: dict) -> "ExtendResponse":
        """Create ExtendResponse from API response data."""
        return cls(
            success=response_data.get("success", False),
            message=response_data.get("message"),
            task_id=response_data.get("taskId") or response_data.get("task_id"),
            audio_url=response_data.get("audioUrl") or response_data.get("audio_url"),
            status=response_data.get("status"),
            estimated_completion_time=response_data.get("estimatedCompletionTime") or 
                                    response_data.get("estimated_completion_time")
        )


class UploadResponse(BaseModel):
    """Response model for file upload operations."""
    
    success: bool = Field(..., description="Whether the upload was successful")
    upload_url: Optional[str] = Field(None, description="URL of the uploaded file")
    file_id: Optional[str] = Field(None, description="Unique identifier for the uploaded file")
    message: Optional[str] = Field(None, description="Response message")
    
    @classmethod
    def from_api_response(cls, response_data: dict) -> "UploadResponse":
        """Create UploadResponse from API response data."""
        return cls(
            success=response_data.get("success", False),
            upload_url=response_data.get("uploadUrl") or response_data.get("upload_url"),
            file_id=response_data.get("fileId") or response_data.get("file_id"),
            message=response_data.get("message")
        )
